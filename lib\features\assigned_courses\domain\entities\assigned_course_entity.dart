import 'package:equatable/equatable.dart';

/// Domain entity for assigned courses (cours assignés) for PRF profile users
class AssignedCourseEntity extends Equatable {
  final String cours;
  final String niveau;
  final String semestre;
  final String? nomDocument;
  final int volumeHoraire;
  final String? idDocument;
  final int volumeHoraireConsomme;
  final String tauxOuForfait;

  const AssignedCourseEntity({
    required this.cours,
    required this.niveau,
    required this.semestre,
    this.nomDocument,
    required this.volumeHoraire,
    this.idDocument,
    required this.volumeHoraireConsomme,
    required this.tauxOuForfait,
  });

  @override
  List<Object?> get props => [
        cours,
        niveau,
        semestre,
        nomDocument,
        volumeHoraire,
        idDocument,
        volumeHoraireConsomme,
        tauxOuForfait,
      ];

  /// Check if the course has a document attached
  bool get hasDocument => idDocument != null && nomDocument != null;

  /// Get formatted level and semester info
  String get levelSemesterInfo => '$niveau | $semestre';
}
