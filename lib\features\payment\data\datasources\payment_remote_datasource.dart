import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:kairos/core/api/api_exception.dart';
import 'package:kairos/core/error/exceptions.dart';

import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/payment_request_model.dart';
import '../models/payment_response_model.dart';

/// Abstract interface for payment remote data source
abstract class PaymentRemoteDataSource {
  /// Process payment request via API
  Future<PaymentResponseModel> processPayment(PaymentRequestModel paymentRequest);
}

/// Implementation of PaymentRemoteDataSource
class PaymentRemoteDataSourceImpl implements PaymentRemoteDataSource {
  final ApiClient apiClient;

  PaymentRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<PaymentResponseModel> processPayment(PaymentRequestModel paymentRequest) async {
    try {
      final response = await apiClient.postWithToken(
        ApiEndpoints.payment,
        data: paymentRequest.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        return PaymentResponseModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw Exception('Failed to process payment: ${response.statusCode}');
      }
    } on DioException catch (e) {
      debugPrint('FinancesRemoteDataSourceImpl: DioException: $e');
      debugPrint('FinancesRemoteDataSourceImpl: DioException response status: ${e.response!.statusCode}');
      final response = e.response!.data;
      debugPrint('FinancesRemoteDataSourceImpl: Decoded response: $response');
      if (response != null) {
        final apiException = ApiException.fromJson(
          response,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        throw NetworkException('Erreur de connexion lors de la récupération de la carte virtuelle: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la récupération de la carte virtuelle: $e');
    }
  }
}
