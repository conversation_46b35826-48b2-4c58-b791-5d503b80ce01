import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/assigned_course_entity.dart';

/// Abstract repository interface for assigned courses operations
abstract class AssignedCoursesRepository {
  /// Get assigned courses for a professor
  Future<Either<Failure, List<AssignedCourseEntity>>> getAssignedCourses({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}
