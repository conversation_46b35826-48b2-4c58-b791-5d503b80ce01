import 'package:equatable/equatable.dart';
import '../../domain/entities/payment_response_entity.dart';

/// Base payment state
abstract class PaymentState extends Equatable {
  const PaymentState();
  
  @override
  List<Object?> get props => [];
}

/// Initial payment state
class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

/// Loading state during payment processing
class PaymentLoading extends PaymentState {
  const PaymentLoading();
}

/// Payment processed successfully
class PaymentSuccess extends PaymentState {
  final PaymentResponseEntity paymentResponse;
  final double paymentAmount;

  const PaymentSuccess({
    required this.paymentResponse,
    required this.paymentAmount,
  });

  @override
  List<Object?> get props => [paymentResponse, paymentAmount];
}

/// Payment processing failed
class PaymentError extends PaymentState {
  final String message;
  
  const PaymentError(this.message);
  
  @override
  List<Object?> get props => [message];
}
