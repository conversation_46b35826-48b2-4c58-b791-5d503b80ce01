import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/assigned_course_entity.dart';
import '../repositories/assigned_courses_repository.dart';

/// Parameters for GetAssignedCoursesUseCase
class GetAssignedCoursesParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetAssignedCoursesParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [codeEtab, telephone, codeEtudiant, codeUtilisateur];
}

/// Use case for getting assigned courses for PRF profile users
class GetAssignedCoursesUseCase implements UseCase<List<AssignedCourseEntity>, GetAssignedCoursesParams> {
  final AssignedCoursesRepository repository;

  const GetAssignedCoursesUseCase(this.repository);

  @override
  Future<Either<Failure, List<AssignedCourseEntity>>> call(
    GetAssignedCoursesParams params,
  ) async {
    return await repository.getAssignedCourses(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
