import 'package:flutter/material.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/assigned_course_entity.dart';
import '../../domain/repositories/assigned_courses_repository.dart';
import '../datasources/assigned_courses_remote_datasource.dart';

/// Implementation of AssignedCoursesRepository
class AssignedCoursesRepositoryImpl implements AssignedCoursesRepository {
  final AssignedCoursesRemoteDataSource remoteDataSource;

  AssignedCoursesRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<AssignedCourseEntity>>> getAssignedCourses({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final assignedCourseModels = await remoteDataSource.getAssignedCourses(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );

      return Right(assignedCourseModels.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      debugPrint('AssignedCoursesRepositoryImpl: ServerException: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      debugPrint('AssignedCoursesRepositoryImpl: NetworkException: ${e.message}');
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
      debugPrint('AssignedCoursesRepositoryImpl: DioException: ${e.message}');
      return Left(ServerFailure('Erreur de connexion lors du chargement des cours assignés: ${e.message}'));
    } catch (e) {
      debugPrint('AssignedCoursesRepositoryImpl: UnexpectedException: ${e.toString()}');
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
