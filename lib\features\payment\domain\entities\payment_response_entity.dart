import 'package:equatable/equatable.dart';
import '../../data/models/payment_response_model.dart';

/// Entity representing a payment response from the API
class PaymentResponseEntity extends Equatable {
  final String paymentUrl;
  final String transactionId;
  final String qrCode;
  final String qrId;

  const PaymentResponseEntity({
    required this.paymentUrl,
    required this.transactionId,
    required this.qrCode,
    required this.qrId,
  });

  /// Factory constructor to create entity from model
  factory PaymentResponseEntity.fromModel(PaymentResponseModel model) {
    return PaymentResponseEntity(
      paymentUrl: model.paymentUrl,
      transactionId: model.transactionId,
      qrCode: model.qrCode,
      qrId: model.qrId,
    );
  }

  @override
  List<Object?> get props => [
    paymentUrl,
    transactionId,
    qrCode,
    qrId,
  ];
}
