/// Payment method enumeration for handling different payment options
enum PaymentMethod { 
  wave, 
  orangeMoney 
}

/// Extension to provide string values for PaymentMethod enum
extension PaymentMethodExtension on PaymentMethod {
  /// Get the API value for the payment method
  String get apiValue {
    switch (this) {
      case PaymentMethod.wave:
        return 'WAVE';
      case PaymentMethod.orangeMoney:
        return 'OM';
    }
  }

  /// Get the display name for the payment method
  String get displayName {
    switch (this) {
      case PaymentMethod.wave:
        return 'Wave';
      case PaymentMethod.orangeMoney:
        return 'Orange Money';
    }
  }
}
