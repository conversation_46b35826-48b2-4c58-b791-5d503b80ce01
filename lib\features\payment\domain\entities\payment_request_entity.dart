import 'package:equatable/equatable.dart';

/// Entity representing a payment request
class PaymentRequestEntity extends Equatable {
  final double montantOperation;
  final String codeEtab;
  final String codeEtudiant;
  final String moyenPaiement;

  const PaymentRequestEntity({
    required this.montantOperation,
    required this.codeEtab,
    required this.codeEtudiant,
    required this.moyenPaiement,
  });

  @override
  List<Object?> get props => [
    montantOperation,
    codeEtab,
    codeEtudiant,
    moyenPaiement,
  ];
}
