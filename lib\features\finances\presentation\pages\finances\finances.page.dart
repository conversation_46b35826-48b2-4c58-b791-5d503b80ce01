import 'package:kairos/core/theme/color_schemes.dart';
import 'package:kairos/core/utils/navigation_utils.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/enums/payment_method.dart';
import 'package:kairos/core/widgets/dialogs/app_alert_dialog.dart';

import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/features/finances/presentation/pages/finances/finances_widgets/finance_item.widget.dart';
import 'package:kairos/features/finances/presentation/pages/finances/finances_widgets/payment_dialog.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/widgets/notifications/success_notification_widget.dart';
import 'package:kairos/core/widgets/notifications/fail_notification_widget.dart';
import 'package:kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:kairos/features/finances/presentation/bloc/finances_state.dart';
import 'package:kairos/features/payment/presentation/bloc/payment_cubit.dart';
import 'package:kairos/features/payment/presentation/bloc/payment_state.dart';
import 'package:kairos/features/finances/domain/entities/frais_etudiant_entity.dart';
import 'package:kairos/features/finances/domain/entities/finances_response_entity.dart';
import 'package:kairos/features/finances/domain/entities/finances_unpaid_response_entity.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/core/di/injection_container.dart';
import 'package:intl/intl.dart';

class FinancesPage extends StatefulWidget{
  final int initialTab;
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;

  const FinancesPage({
    super.key,
    this.initialTab = 0,
    required this.school,
    this.etudiant,
  });

  @override
  State<FinancesPage> createState() => _FinancesPageState();
}



class _FinancesPageState extends State<FinancesPage> with TickerProviderStateMixin{
  late TabController _tabController;
  late bool _isSearchBarVisible = false;
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _searchAnimationController;
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  // Success notification state
  bool _showSuccessNotification = false;
  bool _showFailNotification = false;
  String _notificationMessage = '';

  // Payment tracking state
  double? _previousTotalImpaye;
  double? _paymentAmount;



  @override
  void initState(){
    super.initState();
    _tabController = TabController(initialIndex: widget.initialTab, length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild the widget when tab changes to update FAB visibility
    });
    _searchController.addListener(_filterFinances);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );

    // Load finances data
    _loadFinancesData();
  }

  Future<void> _loadFinancesData() async {
    final phoneNumber = await _authLocalDataSource.getPhoneNumber();

    if (phoneNumber != null && mounted) {
      context.read<FinancesCubit>().loadFinancesData(
        codeEtab: widget.school.codeEtab,
        telephone: phoneNumber,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur, // Use school's codeEtudiant if etudiant is null
        codeUtilisateur: widget.etudiant != null ? widget.school.codeUtilisateur: null,
      );
    }
  }

  @override
  void dispose(){
    _tabController.removeListener(() {}); // Remove the listener
    _tabController.dispose();
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _showPaymentDialog(FinancesUnpaidResponseEntity unpaidFeesResponse) {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).canvasColor,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16.0)),
              ),
              child: PaymentModal(
                unpaidFeesResponse: unpaidFeesResponse,
                onPaymentConfirmed: _onPaymentConfirmed,
              ),
            );
          },
        );
      },
    );
  }

  void _onPaymentConfirmed(double amount, PaymentMethod paymentMethod) async {
    // Store current total impayé for comparison after payment
    final currentState = context.read<FinancesCubit>().state;
    if (currentState is FinancesLoaded && currentState.unpaidFeesResponse != null) {
      _previousTotalImpaye = currentState.unpaidFeesResponse!.totalImpaye ?? 0.0;
      _paymentAmount = amount;
    }

    // Get required data for payment processing
    final phoneNumber = await _authLocalDataSource.getPhoneNumber();
    if (phoneNumber == null) {
      _showPaymentError('Impossible de récupérer les informations utilisateur');
      return;
    }

    // Process payment using PaymentCubit
    if (mounted) {
      context.read<PaymentCubit>().processPayment(
        amount: amount,
        paymentMethod: paymentMethod,
        codeEtab: widget.school.codeEtab,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur,
      );
    }

    // Wait a moment for payment processing, then refresh finances data
    await Future.delayed(const Duration(seconds: 10));
    if (mounted) {
      _loadFinancesData();
      _verifyPaymentSuccess();
    }
  }

  /// Show payment error notification
  void _showPaymentError(String message) {
    setState(() {
      _showFailNotification = true;
      _notificationMessage = message;
    });

    // Switch to unpaid tab to show the notification
    if (_tabController.index != 0) {
      _tabController.animateTo(0);
    }
  }

  /// Verify payment success by comparing total impayé values
  void _verifyPaymentSuccess() {
    final currentState = context.read<FinancesCubit>().state;
    if (currentState is FinancesLoaded &&
        currentState.unpaidFeesResponse != null &&
        _previousTotalImpaye != null &&
        _paymentAmount != null) {

      final newTotalImpaye = currentState.unpaidFeesResponse!.totalImpaye ?? 0.0;
      final expectedReduction = _paymentAmount!;
      final actualReduction = _previousTotalImpaye! - newTotalImpaye;

      // Check if payment was successful
      if (actualReduction >= expectedReduction || newTotalImpaye < _previousTotalImpaye!) {
        // Payment successful
        setState(() {
          _showSuccessNotification = true;
          _showFailNotification = false;
        });
      } else {
        // Payment failed or not processed
        setState(() {
          _showFailNotification = true;
          _showSuccessNotification = false;
        });
      }

      // Switch to unpaid tab to show the notification
      if (_tabController.index != 0) {
        _tabController.animateTo(0);
      }

      // Reset payment tracking variables
      _previousTotalImpaye = null;
      _paymentAmount = null;
    }
  }

  void _filterFinances() {
    // This will be handled by BlocBuilder now
    // The filtering logic will be moved to the BlocBuilder
  }

  List<FraisEtudiantEntity> _filterFeesList(
    List<FraisEtudiantEntity> fees,
    String query,
    bool isPaidTab
  ) {
    return fees.where((fee) {
      // Text search filter only (date filtering is now handled server-side)
      bool matchesText = fee.intituleFrais.toLowerCase().contains(query);
      return matchesText;
    }).toList();
  }

  Widget _buildFeesTab(List<FraisEtudiantEntity> fees, bool isPaidTab, {FinancesResponseEntity? paidFeesResponse, FinancesUnpaidResponseEntity? unpaidFeesResponse}) {
    if (fees.isEmpty) {
      return Center(
        child: EmptyMessage(
          message: isPaidTab ? "Aucun frais payé trouvé" : "Aucun frais impayé trouvé",
        ),
      );
    }

    // Build the list of slivers for the CustomScrollView
    List<Widget> slivers = [];

    // Add summary text only for the paid tab if data is available
    if (isPaidTab && paidFeesResponse != null) {
      final montantEncaisser = paidFeesResponse.montantEncaisser ?? 0;
      final montantAEncaisser = paidFeesResponse.montantAEncaisser ?? 0;
      final anneeReference = paidFeesResponse.anneeReference ?? 'N/A'; // Handle null year

      final summaryText = "${NumberFormat.decimalPattern('fr_FR').format(montantEncaisser)} XOF sur ${NumberFormat.decimalPattern('fr_FR').format(montantAEncaisser)} XOF encaissés pour $anneeReference";

      slivers.add(
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              summaryText,
              style: TextStyle(color: AppColorSchemes.errorRed, 
              fontSize: Theme.of(context).textTheme.titleSmall?.fontSize), // Adjust style as needed
            ),
          ),
        ),
      );
    }
    // Add summary text for unpaid tab if data is available
    if (!isPaidTab && unpaidFeesResponse != null) {
      final totalImpaye = unpaidFeesResponse.totalImpaye ?? 0;
      final summaryText = "TOTAL IMPAYÉ: ${NumberFormat.decimalPattern('fr_FR').format(totalImpaye)} XOF";
      slivers.add(
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              summaryText,
              style: TextStyle(color: AppColorSchemes.errorRed,
              fontSize: Theme.of(context).textTheme.titleSmall?.fontSize), // Adjust style as needed
              ),
            ),
          ),
        );
    }

    // Add success notification for unpaid tab if visible
    if (!isPaidTab && _showSuccessNotification) {
      slivers.add(
        SliverToBoxAdapter(
          child: SuccessNotificationWidget(
            title: "CONFIRMATION DE PAIEMENT",
            message: "Votre paiement a été effectué avec succès !",
            onDismiss: () {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                setState(() {
                  _showSuccessNotification = false;
                });
              });
            },
          ),
        ),
      );
    }

    // Add fail notification for unpaid tab if visible
    if (!isPaidTab && _showFailNotification) {
      if(_notificationMessage.isEmpty){
        _notificationMessage = "Votre paiement n'a pas pu être traité. Veuillez réessayer.";
      } 
      slivers.add(
        SliverToBoxAdapter(
          child: FailNotificationWidget(
            title: "ÉCHEC DU PAIEMENT",
            message: _notificationMessage,
            onDismiss: () {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                setState(() {
                  _showFailNotification = false;
                });
              });
            },
          ),
        ),
      );
    }

    // Add the list of finance items
    slivers.add(
      SliverPadding(
        padding: const EdgeInsets.only(top: 2.0, bottom: 2.0),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final fee = fees[index];
              return FinanceItem(
                intituleFrais: fee.intituleFrais,
                montantFrais: (fee.montantFrais ?? 0).toInt(),
                status: isPaidTab ? 'Quittance générée' : 'Aucune quittance générée',
                isPaid: isPaidTab,
                dateEchance: fee.dateEchance,
                isObligatory: fee.indicateur?? false, // Default to true since we don't have this field
                montantEncaisseAjour: fee.montantEncaisseAjour?.toInt(),
                dateQuittance: fee.dateQuittance,
                numeroQuittance: fee.numeroQuittance,
              );
            },
            childCount: fees.length,
          ),
        ),
      ),
    );


    return CustomScrollView(
      slivers: slivers,
    );
  }



  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    // Trigger server-side filtering with date range
    _loadFinancesDataWithDateFilter();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Load finances data without date filter (back to normal loading)
    _loadFinancesData();
  }

  // Load finances data with date filter (server-side filtering)
  Future<void> _loadFinancesDataWithDateFilter() async {
    if (_startDateFilter == null || _endDateFilter == null) {
      // If no date filter, use normal loading
      _loadFinancesData();
      return;
    }

    final phoneNumber = await _authLocalDataSource.getPhoneNumber();

    if (phoneNumber != null && mounted) {
      context.read<FinancesCubit>().loadFinancesDataWithDateFilter(
        codeEtab: widget.school.codeEtab,
        telephone: phoneNumber,
        dateDebut: _startDateFilter!,
        dateFin: _endDateFilter!,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur, // Use school's codeEtudiant if etudiant is null
        codeUtilisateur: widget.etudiant != null ? widget.school.codeUtilisateur: null,
      );
    }
  }

  @override
  Widget build(BuildContext context){
    return BlocListener<PaymentCubit, PaymentState>(
      listener: (context, state) {
        if (state is PaymentSuccess) {
          _loadFinancesData();
          _verifyPaymentSuccess();
        } else if (state is PaymentError) {
          if (state.message.contains('token') && state.message.contains('expiré')){
        // display error dialog
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AppAlertDialog(
              title: "SESSION EXPIRÉE",
              content: const Text("Votre session a expiré. Veuillez vous reconnecter.", textAlign: TextAlign.center,),
              primaryActionCallback: () async {
                await NavigationUtils.pushNamed(
                  context,
                  "/accueil",
                  arguments: {'goToPhoneAuth': true},
                );
              },
            );
          },
        );
      } else {
          _showPaymentError(state.message);
        }
      }
      },
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            CustomAppBar(pageSection: HeaderEnum.finances, title: "SITUATION FINANCIÈRE",
              isSearchBarVisible: _isSearchBarVisible,
              etablissementUtilisateur: widget.school,
              enfantDuTuteur: widget.etudiant,
              onSearchTap: () {
                setState(() {
                  _isSearchBarVisible = !_isSearchBarVisible;
                  if (!_isSearchBarVisible) {
                    _searchAnimationController.reverse();
                    _searchController.clear(); // Clear search when hidden
                    _filterFinances(); // Reset filter
                  } else {
                    _searchAnimationController.forward();
                  }
                });
              },
            ),
            SliverToBoxAdapter(
              child:
            TabBar(
              controller: _tabController,
              indicatorColor: Colors.amber,
              indicatorSize: TabBarIndicatorSize.tab,
              tabs: [
                Tab(text: "FRAIS IMPAYÉS"),
                Tab(text: "FRAIS PAYÉS"),
              ],
            ),
        ),
            AnimatedBuilder(
              animation: _searchAnimationController,
              builder: (context, child) {
                return SliverPersistentHeader(
              pinned: true,
              delegate: SearchBarSliver(
              hintText: _tabController.index == 0? "Rechercher frais payés": _tabController.index == 1? "Rechercher frais impayés": "Rechercher ...",
              extentHeight: _searchAnimationController.value * (_startDateFilter != null && _endDateFilter != null ? 100.0 : 60.0),
              searchController: _searchController,
              onSearchChanged: (query) => _filterFinances(),
              onDateFilterChanged: _onDateFilterChanged,
              onClearDateFilter: _clearDateFilter,
              hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
              startDate: _startDateFilter,
              endDate: _endDateFilter,
            ),);
              },
            ),
          SliverFillRemaining(
            child: BlocBuilder<FinancesCubit, FinancesState>(
              builder: (context, state) {
                if (state is FinancesLoading) {
                  debugPrint("FinancesLoading ---> Loading finances data...");
                  return const Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  );
                } else if (state is FinancesError) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: AppColorSchemes.errorRed,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Erreur lors du chargement',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadFinancesData,
                            child: const Text('Réessayer'),
                          ),
                        ],
                      ),
                    ),
                  );
                } else if (state is FinancesLoaded) {
                  final query = _searchController.text.toLowerCase();
                  debugPrint("FinancesLoaded ---> Finances data loaded. Query: $query");
                  debugPrint("FinancesLoaded ---> Paid fees: ${state.paidFeesResponse?.fraisPayes}");
                  debugPrint("FinancesLoaded ---> UnPaid fees: ${state.unpaidFeesResponse?.fraisImPayes}");
                  final filteredPaidFees = _filterFeesList(state.paidFeesResponse?.fraisPayes ?? [], query, true);
                  final filteredUnpaidFees = _filterFeesList(state.unpaidFeesResponse?.fraisImPayes ?? [], query, false);

                  return TabBarView(
                    controller: _tabController,
                    children: [
                      // Unpaid fees tab
                      _buildFeesTab(filteredUnpaidFees, false, unpaidFeesResponse: state.unpaidFeesResponse),
                      // Paid fees tab
                      _buildFeesTab(filteredPaidFees, true, paidFeesResponse: state.paidFeesResponse),
                    ],
                  );
                }

                return const SizedBox(); // Fallback in case of unexpected state
              },
            ),
          ),
          ],
        ),
        bottomNavigationBar:
           Padding(
             padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
             child: BlocBuilder<PaymentCubit, PaymentState>(
                     builder: (context, paymentState) {
              bool isVisible = false;
              if (context.watch<FinancesCubit>().state is FinancesLoaded) {
                final financesState = context.watch<FinancesCubit>().state as FinancesLoaded;
                isVisible = _tabController.index == 0 && (financesState.unpaidFeesResponse?.fraisImPayes?.isNotEmpty ?? false);
              }
              return Visibility(
                visible: isVisible,
                child: FilledButton(
                        style: ButtonStyle(
                          backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                          minimumSize: WidgetStateProperty.all(const Size(200, 50)),
                          shape: WidgetStateProperty.all(
                            RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                          )),
                onPressed: paymentState is PaymentLoading ? null : () {
                  final financesState = context.read<FinancesCubit>().state;
                  if (financesState is FinancesLoaded && financesState.unpaidFeesResponse != null) {
                    _showPaymentDialog(financesState.unpaidFeesResponse!);
                  }
                }, child: paymentState is PaymentLoading
                          ? const CustomSpinner(size: 24.0, strokeWidth: 3.0)
                          : const Text(
                              "PAYER",
                              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                ),
              );
                     },
                   ),
             ),
      ),
    );
  }
}