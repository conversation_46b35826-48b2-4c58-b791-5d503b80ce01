import '../models/assigned_course_model.dart';

/// Abstract interface for assigned courses remote data source
abstract class AssignedCoursesRemoteDataSource {
  /// Get assigned courses from remote API
  Future<List<AssignedCourseModel>> getAssignedCourses({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of AssignedCoursesRemoteDataSource with mock data
/// This will be replaced with actual API calls when the server endpoint is available
class AssignedCoursesRemoteDataSourceImpl implements AssignedCoursesRemoteDataSource {
  @override
  Future<List<AssignedCourseModel>> getAssignedCourses({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Mock data based on Figma design
    return [
      AssignedCourseModel(
        cours: 'Réseaux et informatiques',
        niveau: 'LPTI | Licence 3 | LPTI 1A',
        semestre: 'Semestre 1',
        nomDocument: 'cours_ccna1_MAJ.pdf',
        volumeHoraire: 30,
        idDocument: 'doc_001',
        volumeHoraireConsomme: 3,
        tauxOuForfait: '10 000',
      ),
      AssignedCourseModel(
        cours: 'Programmation réseaux',
        niveau: 'LPTI | Licence 3 | LPTI 1A',
        semestre: 'Semestre 1',
        nomDocument: null, // No document attached
        volumeHoraire: 25,
        idDocument: null,
        volumeHoraireConsomme: 0,
        tauxOuForfait: '8 500',
      ),
      AssignedCourseModel(
        cours: 'Sécurité des applications',
        niveau: 'LPTI | Licence 3 | LPTI 1A',
        semestre: 'Semestre 1',
        nomDocument: 'securite_apps_guide.pdf',
        volumeHoraire: 20,
        idDocument: 'doc_002',
        volumeHoraireConsomme: 5,
        tauxOuForfait: '12 000',
      ),
      AssignedCourseModel(
        cours: 'Active Directory',
        niveau: 'LPTI | Licence 3 | LPTI 1A',
        semestre: 'Semestre 1',
        nomDocument: null, // No document attached
        volumeHoraire: 15,
        idDocument: null,
        volumeHoraireConsomme: 2,
        tauxOuForfait: '7 500',
      ),
      AssignedCourseModel(
        cours: 'Cloud Computing',
        niveau: 'LPTI | Licence 3 | LPTI 1A',
        semestre: 'Semestre 1',
        nomDocument: 'cloud_computing_basics.pdf',
        volumeHoraire: 35,
        idDocument: 'doc_003',
        volumeHoraireConsomme: 8,
        tauxOuForfait: '15 000',
      ),
      AssignedCourseModel(
        cours: 'Management de projet',
        niveau: 'LPTI | Licence 3 | LPTI 1A',
        semestre: 'Semestre 1',
        nomDocument: null, // No document attached
        volumeHoraire: 18,
        idDocument: null,
        volumeHoraireConsomme: 1,
        tauxOuForfait: '9 000',
      ),
    ];
  }
}
