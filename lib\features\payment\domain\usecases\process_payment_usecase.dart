import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/payment_request_entity.dart';
import '../entities/payment_response_entity.dart';
import '../repositories/payment_repository.dart';

/// Use case for processing payment requests
class ProcessPaymentUseCase implements UseCase<PaymentResponseEntity, ProcessPaymentParams> {
  final PaymentRepository repository;

  ProcessPaymentUseCase(this.repository);

  @override
  Future<Either<Failure, PaymentResponseEntity>> call(ProcessPaymentParams params) async {
    final paymentRequest = PaymentRequestEntity(
      montantOperation: params.montantOperation,
      codeEtab: params.codeEtab,
      codeEtudiant: params.codeEtudiant,
      moyenPaiement: params.moyenPaiement,
    );

    return await repository.processPayment(paymentRequest);
  }
}

/// Parameters for the ProcessPaymentUseCase
class ProcessPaymentParams extends Equatable {
  final double montantOperation;
  final String codeEtab;
  final String codeEtudiant;
  final String moyenPaiement;

  const ProcessPaymentParams({
    required this.montantOperation,
    required this.codeEtab,
    required this.codeEtudiant,
    required this.moyenPaiement,
  });

  @override
  List<Object?> get props => [
    montantOperation,
    codeEtab,
    codeEtudiant,
    moyenPaiement,
  ];
}
