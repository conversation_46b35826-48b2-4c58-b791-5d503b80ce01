import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/enums/payment_method.dart';
import '../../domain/usecases/process_payment_usecase.dart';
import 'payment_state.dart';

/// Payment Cubit for managing payment state
class PaymentCubit extends Cubit<PaymentState> {
  final ProcessPaymentUseCase processPaymentUseCase;

  PaymentCubit({
    required this.processPaymentUseCase,
  }) : super(const PaymentInitial());

  /// Process a payment request
  Future<void> processPayment({
    required double amount,
    required PaymentMethod paymentMethod,
    required String codeEtab,
    required String codeEtudiant,
  }) async {
    emit(const PaymentLoading());

    try {
      final result = await processPaymentUseCase(
        ProcessPaymentParams(
          montantOperation: amount,
          codeEtab: codeEtab,
          codeEtudiant: codeEtudiant,
          moyenPaiement: paymentMethod.apiValue,
        ),
      );

      result.fold(
        (failure) => emit(PaymentError(_mapFailureToMessage(failure))),
        (paymentResponse) async {
          // Launch payment URL as deeplink
          await _launchPaymentUrl(paymentResponse.paymentUrl);
          
          emit(PaymentSuccess(
            paymentResponse: paymentResponse,
            paymentAmount: amount,
          ));
        },
      );
    } catch (e) {
      emit(PaymentError('Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Launch payment URL using url_launcher
  Future<void> _launchPaymentUrl(String paymentUrl) async {
    try {
      final uri = Uri.parse(paymentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication, // Launch in external app
        );
      } else {
        throw Exception('Could not launch payment URL');
      }
    } catch (e) {
      // Log error but don't fail the payment process
      print('Failed to launch payment URL: $e');
    }
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return 'Erreur du serveur: ${failure.message}';
    } else if (failure is NetworkFailure) {
      return 'Erreur de connexion. Vérifiez votre connexion internet: ${failure.message}';
    } else {
      return 'Une erreur inattendue s\'est produite lors du paiement.';
    }
  }

  /// Reset payment state
  void resetPayment() {
    emit(const PaymentInitial());
  }
}
