import '../models/dashboard_model.dart';
import '../dashboard_item_type.enum.dart';

/// Mock service for PRF (Enseignant) dashboard data
/// Provides mock data that matches the Figma design specifications
class PrfDashboardMockService {
  /// Generate mock dashboard data for PRF users based on Figma design
  static DashboardModel getMockPrfDashboardData() {
    return DashboardModel(
      anneeScolaire: '2024-2025',
      
      // Mock course assignments data (Item 1 from Figma: "12 cours assignés pour 2024-2025")
      note: NoteModel(nombreNote: 12), // Reusing note field for course assignments
      
      // Mock pointages data (Item 2 from Figma: "27 pointages enregistrés pour 2024-2025")
      assiduite: AssiduitModel(nombreAbsence: 27, nombreRetard: 0), // Reusing assiduite for pointages
      
      // Mock financial data (Item 3 from Figma: "06 paiements enregistrés pour 2024-2025")
      etatFinancier: EtatFinancierModel(
        montantEncaisse: 6.0, // Using montantEncaisse for payment count
        montantAencaisser: 0.0,
        montantImpaye: 0.0,
      ),
      
      // Mock evaluations data (Item 4 from Figma: "35 évaluations enregistrées pour 2024-2025")
      dossier: DossierModel(nombreElement: 35), // Reusing dossier for evaluations
      
      // Mock resources data (Item 5 from Figma: "3 ressources pédagogiques pour 2024-2025")
      ressourcesPedagogiques: RessourcesPedagogiquesModel(nombreRessource: 3),
      
      // Mock dossier elements (Item 10 from Figma: "5 éléments ajoutés à votre dossier pour 2024-2025")
      // We'll use cahierTexte for this since we need another field
      cahierTexte: CahierTexteModel(nombreEntre: 5, semestre: '2024-2025'),
      
      // Mock planning data (Item 6 from Figma: "7 plannings pour la semaine dont 2 pour la journée")
      plannings: PlanningsModel(
        nombrePlanningSemaine: 7,
        nombrePlanningJour: 2,
      ),
    );
  }

  /// Generate mock dashboard items for PRF users
  static List<Map<String, dynamic>> getMockPrfDashboardItems() {
    final mockData = getMockPrfDashboardData();
    
    return [
      // Item 1: Course assignments (using book icon)
      {
        "title": "${mockData.note.nombreNote} cours assignés pour ${mockData.anneeScolaire}",
        "icon": "icone_cahier_de_texte.svg", // Book icon
        "itemType": DashboardItemType.coursAssignes,
      },
      
      // Item 2: Pointages (using business time icon)
      {
        "title": "${mockData.assiduite.nombreAbsence} pointages enregistrés pour ${mockData.anneeScolaire}",
        "icon": "icone_planning_cours.svg", // Business time icon
        "itemType": DashboardItemType.pointage,
      },
      
      // Item 3: Payments (using coins icon)
      {
        "title": "${mockData.etatFinancier.montantEncaisse.toInt()} paiements enregistrés pour ${mockData.anneeScolaire}",
        "icon": "icone_finance.svg", // Stack of coins icon
        "itemType": DashboardItemType.finances,
      },
      
      // Item 4: Evaluations (using list view icon)
      {
        "title": "${mockData.dossier.nombreElement} évaluations enregistrées pour ${mockData.anneeScolaire}",
        "icon": "icone_notes.svg", // List view icon
        "itemType": DashboardItemType.notes,
      },
      
      // Item 5: Educational resources (using document icon)
      {
        "title": "${mockData.ressourcesPedagogiques.nombreRessource} ressources pédagogiques pour ${mockData.anneeScolaire}",
        "icon": "icone_ressources_telechargeable.svg", // Document icon
        "itemType": DashboardItemType.ressources,
      },
      
      // Item 10: Dossier elements (using folder icon)
      {
        "title": "${mockData.cahierTexte.nombreEntre} éléments ajoutés à votre dossier pour ${mockData.anneeScolaire}",
        "icon": "icone_dossier_etudiant.svg", // Folder icon
        "itemType": DashboardItemType.dossiers,
      },
      
      // Item 8: Absences and tardiness (using business time icon)
      {
        "title": "2 absences et 1 retard enregistrés pour ${mockData.anneeScolaire}",
        "icon": "icone_absences.svg", // Business time icon
        "itemType": DashboardItemType.absences,
      },
      
      // Item 6: Planning (using business time icon)
      {
        "title": "${mockData.plannings.nombrePlanningSemaine} plannings pour la semaine dont ${mockData.plannings.nombrePlanningJour} pour la journée",
        "icon": "icone_planning_cours.svg", // Business time icon
        "itemType": DashboardItemType.planning,
      },
      
      // Item 9: Virtual card (using smart card icon)
      {
        "title": "Ma carte virtuelle",
        "icon": "icone_carte_virtuelle.svg", // Smart card icon
        "itemType": DashboardItemType.carteVirtuelle,
      },
    ];
  }
}
