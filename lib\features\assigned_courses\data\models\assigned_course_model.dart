import '../../domain/entities/assigned_course_entity.dart';

/// Data model for assigned courses
class AssignedCourseModel {
  final String cours;
  final String niveau;
  final String semestre;
  final String? nomDocument;
  final int volumeHoraire;
  final String? idDocument;
  final int volumeHoraireConsomme;
  final String tauxOuForfait;

  AssignedCourseModel({
    required this.cours,
    required this.niveau,
    required this.semestre,
    this.nomDocument,
    required this.volumeHoraire,
    this.idDocument,
    required this.volumeHoraireConsomme,
    required this.tauxOuForfait,
  });

  factory AssignedCourseModel.fromJson(Map<String, dynamic> json) {
    return AssignedCourseModel(
      cours: json['cours'] ?? '',
      niveau: json['niveau'] ?? '',
      semestre: json['semestre'] ?? '',
      nomDocument: json['nomDocument'],
      volumeHoraire: json['volumeHoraire'] ?? 0,
      idDocument: json['idDocument'],
      volumeHoraireConsomme: json['volumeHoraireConsomme'] ?? 0,
      tauxOuForfait: json['tauxOuForfait'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cours': cours,
      'niveau': niveau,
      'semestre': semestre,
      'nomDocument': nomDocument,
      'volumeHoraire': volumeHoraire,
      'idDocument': idDocument,
      'volumeHoraireConsomme': volumeHoraireConsomme,
      'tauxOuForfait': tauxOuForfait,
    };
  }

  /// Convert model to entity
  AssignedCourseEntity toEntity() {
    return AssignedCourseEntity(
      cours: cours,
      niveau: niveau,
      semestre: semestre,
      nomDocument: nomDocument,
      volumeHoraire: volumeHoraire,
      idDocument: idDocument,
      volumeHoraireConsomme: volumeHoraireConsomme,
      tauxOuForfait: tauxOuForfait,
    );
  }
}
