import 'package:equatable/equatable.dart';
import '../../domain/entities/assigned_course_entity.dart';

/// Base state for assigned courses
abstract class AssignedCoursesState extends Equatable {
  const AssignedCoursesState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class AssignedCoursesInitial extends AssignedCoursesState {
  const AssignedCoursesInitial();
}

/// Loading state
class AssignedCoursesLoading extends AssignedCoursesState {
  const AssignedCoursesLoading();
}

/// Loaded state with assigned courses data
class AssignedCoursesLoaded extends AssignedCoursesState {
  final List<AssignedCourseEntity> assignedCourses;
  final List<AssignedCourseEntity> filteredCourses;

  const AssignedCoursesLoaded({
    required this.assignedCourses,
    required this.filteredCourses,
  });

  @override
  List<Object?> get props => [assignedCourses, filteredCourses];

  /// Copy with method for updating filtered courses
  AssignedCoursesLoaded copyWith({
    List<AssignedCourseEntity>? assignedCourses,
    List<AssignedCourseEntity>? filteredCourses,
  }) {
    return AssignedCoursesLoaded(
      assignedCourses: assignedCourses ?? this.assignedCourses,
      filteredCourses: filteredCourses ?? this.filteredCourses,
    );
  }
}

/// Error state
class AssignedCoursesError extends AssignedCoursesState {
  final String message;

  const AssignedCoursesError(this.message);

  @override
  List<Object?> get props => [message];
}
