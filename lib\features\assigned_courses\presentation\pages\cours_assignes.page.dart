import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../core/widgets/common/hero_widget.dart';
import '../../../../core/widgets/common/search_bar_sliver.dart';
import '../../../../features/schools/domain/entities/etablissement_utilisateur.dart';
import '../../../../features/student_records/domain/entities/enfant_tuteur_entity.dart';
import '../bloc/assigned_courses_cubit.dart';
import '../bloc/assigned_courses_state.dart';
import '../widgets/assigned_course_item.widget.dart';
import '../widgets/course_detail_dialog.widget.dart';

/// Page for displaying assigned courses for PRF profile users
class CoursAssignesPage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;

  const CoursAssignesPage({
    Key? key,
    required this.school,
    this.etudiant,
  }) : super(key: key);

  @override
  State<CoursAssignesPage> createState() => _CoursAssignesPageState();
}

class _CoursAssignesPageState extends State<CoursAssignesPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadAssignedCourses();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Load assigned courses data
  void _loadAssignedCourses() {
    context.read<AssignedCoursesCubit>().loadAssignedCourses(
          codeEtab: widget.school.codeEtab,
          telephone: widget.school.telephone,
          codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeEtudiant,
          codeUtilisateur: widget.etudiant?.codeUtilisateur,
        );
  }

  /// Handle search query changes
  void _onSearchChanged(String query) {
    context.read<AssignedCoursesCubit>().filterCourses(query);
  }

  /// Handle refresh
  Future<void> _onRefresh() async {
    _loadAssignedCourses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // Hero widget with header
          SliverToBoxAdapter(
            child: HeroWidget(
              school: widget.school,
              title: "COURS ASSIGNÉS",
              icon: SvgPicture.asset(
                "assets/icons/icone_cahier_de_texte.svg",
                width: 48,
                height: 45,
                fit: BoxFit.fill,
              ),
            ),
          ),
          // Search bar
          SearchBarSliver(
            controller: _searchController,
            onChanged: _onSearchChanged,
            hintText: "Rechercher un cours...",
          ),
          // Content
          BlocBuilder<AssignedCoursesCubit, AssignedCoursesState>(
            builder: (context, state) {
              if (state is AssignedCoursesLoading) {
                return const SliverFillRemaining(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              } else if (state is AssignedCoursesError) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          state.message,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadAssignedCourses,
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  ),
                );
              } else if (state is AssignedCoursesLoaded) {
                if (state.filteredCourses.isEmpty) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.school_outlined,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Aucun cours assigné trouvé',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 10, 16, 16),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final course = state.filteredCourses[index];
                        return AssignedCourseItem(
                          course: course,
                          onTap: () => _showCourseDetailDialog(course),
                        );
                      },
                      childCount: state.filteredCourses.length,
                    ),
                  ),
                );
              }

              return const SliverFillRemaining(
                child: Center(
                  child: Text('État inattendu'),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Show course detail dialog
  void _showCourseDetailDialog(course) {
    showDialog(
      context: context,
      builder: (context) => CourseDetailDialog(course: course),
    );
  }
}
