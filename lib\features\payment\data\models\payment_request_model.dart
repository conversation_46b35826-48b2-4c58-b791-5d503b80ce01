import '../../../payment/domain/entities/payment_request_entity.dart';

/// Model for payment request data
class PaymentRequestModel {
  final double montantOperation;
  final String codeEtab;
  final String codeEtudiant;
  final String moyenPaiement;

  PaymentRequestModel({
    required this.montantOperation,
    required this.codeEtab,
    required this.codeEtudiant,
    required this.moyenPaiement,
  });

  /// Convert entity to model
  factory PaymentRequestModel.fromEntity(PaymentRequestEntity entity) {
    return PaymentRequestModel(
      montantOperation: entity.montantOperation,
      codeEtab: entity.codeEtab,
      codeEtudiant: entity.codeEtudiant,
      moyenPaiement: entity.moyenPaiement,
    );
  }

  /// Convert model to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'montantOperation': montantOperation,
      'codeEtab': codeEtab,
      'codeEtudiant': codeEtudiant,
      'moyenPaiement': moyenPaiement,
    };
  }
}
